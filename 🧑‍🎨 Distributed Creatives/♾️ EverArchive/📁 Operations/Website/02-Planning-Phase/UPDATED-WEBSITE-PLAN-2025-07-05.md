# EverArchive Website Launch Plan - Updated Structure

**Date**: July 5, 2025  
**Status**: Updated with foundations-first approach and staged implementation  
**Deadline**: TODAY - July 5, 2025  
**Key Change**: DAP (Deep Authorship Package) replaces .dao format references  

---

## Strategic Approach - REVISED

### Key Insights from Analysis:
1. **Foundations Missing**: Current site has features but missing foundational concepts like Deep Authorship 3-Layer Model
2. **EverArchive Works WITH Storage Providers**: We are infrastructure, not storage - critical positioning 
3. **Software/Tools Essential**: Need to highlight free, open-source tools for onboarding/consumption
4. **Current Site Had Good Elements**: ".dao format", "encrypted private thoughts, verifiable history" were actually describing foundations

### Structure Philosophy:
**Hero → Foundations → Features → Software/Tools → Technology → About/Mission → Resources**

This ensures visitors understand the unique building blocks BEFORE seeing applications.

---

## Stage 1: Basic Landing Page (TODAY'S PRIORITY)

### 1. Hero Section
**Headline**: "The Infrastructure for Civilizational Memory"  
**Subheadline**: "Preserve Human Creativity Forever"  
**Description**: EverArchive provides permanent infrastructure for preserving the complete creative process—not just final works, but the entire journey of human creativity.  
**CTAs**: "Join the Preservation Infrastructure" / "Download White Paper"

### 2. Foundations Section ⭐️ NEW
**Purpose**: Establish the unique building blocks that everything else depends on  
**Content**: [WAITING ON RESEARCH AGENT - Foundational Concepts]
- Deep Authorship 3-Layer Model (Core/Process/Surface)
- Creator Sovereignty (zero-knowledge encryption)  
- Infrastructure not Platform (non-profit, partnership approach)
- Permanent Preservation (work with storage providers)
- Verifiable Provenance (cryptographic proof)

### 3. Features Section
**Purpose**: Show applications of the foundational concepts  
**Content**: Distilled from 76+ feature library into 6 compelling capability areas
- Creative Control & Ownership (14 features)
- Preservation & Permanence (12 features) 
- Research & Institutional Infrastructure (20 features)
- Library & Digital Lending (7 features)
- Cultural Heritage & Knowledge (7 features)
- Economic Sustainability (9 features)

### 4. Software/Tools Section ⭐️ NEW
**Purpose**: Show EverArchive as infrastructure provider with ecosystem of tools  
**Content**: [NEED TO RESEARCH FROM CANONICAL LIBRARY]
- Free, open-source onboarding tools
- Consumption/access tools
- Developer SDKs and APIs
- Integration capabilities
- Community-built applications

### 5. Technology Section
**Purpose**: How we work with others, interoperability focus  
**Content**: [NEED TO CLARIFY FROM CANONICAL LIBRARY]
- Standards integration (METS, MODS, Dublin Core, PREMIS)
- Schema Projector (format translation)
- Storage partnerships (work WITH Arweave, IPFS, etc.)
- Institutional system compatibility
- Open protocols and APIs

### 6. About/Mission Section
**Purpose**: Brief positioning as infrastructure for civilization  
**Content**: [NEED TO EXTRACT FROM CANONICAL LIBRARY]
- Mission statement extract
- Non-profit positioning
- "Why this matters" messaging
- Civilizational memory framing

### 7. Resources Section  
**Purpose**: Conference materials and getting started  
**Content**: [NEED TO INVENTORY WHAT EXISTS]
- White paper download (needs PDF generation?)
- Technical documentation
- Getting started guides
- Contact information

### 8. Simple Footer
**Content**: Basic contact, minimal links only

---

## Stage 2: Enhanced Version (If Time Permits)

### Additional Enhancements:
- **User Journeys Hero Slider**: Different user journey summaries
- **Secondary Pages**: About, Resources, Software as separate pages
- **Expanded Mission**: Full mission content from canonical library
- **Enhanced Software/Tools**: Detailed tool documentation
- **Partnership Section**: When Archive.org collaboration confirmed

---

## Immediate Research Required

### 1. SOFTWARE/TOOLS Content Deep Dive
**Search for**:
- Capture tools documentation
- User onboarding tools
- Developer SDKs/APIs
- Open-source tool ecosystem
- Integration capabilities

### 2. ABOUT/MISSION Content Extraction  
**Sources**:
- Manifesto documents
- Vision statements
- "Why this matters" content
- Civilizational memory messaging

### 3. TECHNOLOGY Positioning Clarification
**Focus on**:
- How EverArchive works WITH storage providers (not as one)
- Standards interoperability approach
- Integration with existing institutional systems
- Open protocols vs. proprietary platforms

### 4. RESOURCES Inventory
**What exists**:
- White paper status and content
- Technical documentation ready for publication
- Getting started materials
- User guides and tutorials

---

## Critical Updates from Current Site

### ✅ Keep These Elements:
- "Counter data loss and AI's shallow datasets" (strong positioning)
- "Revolutionary DAP format" (now Deep Authorship Package)  
- "encrypted private thoughts, verifiable history, and contextual public work" (describes 3-layer model)
- Permanence messaging and value props

### ⚠️ Fix These Issues:
- Over-specification of particular blockchains/storage (we work WITH providers)
- Missing foundational concepts explanation
- Features without foundation context
- Creator-only focus (needs institutional appeal)

### 🆕 Add These Elements:
- Foundational concepts BEFORE features
- Software/tools ecosystem positioning
- Infrastructure vs. platform distinction
- Standards interoperability emphasis

---

## Success Criteria

### Stage 1 Complete When:
- [x] Foundational concepts researched and documented
- [ ] Software/tools content researched and documented  
- [ ] About/mission content extracted and positioned
- [ ] Technology section clarified (partnership approach)
- [ ] Resources inventory completed
- [ ] V0-ready deployment content updated with new structure

### Strategic Goals Achieved:
- Visitors understand foundational concepts before features
- EverArchive positioned as infrastructure working WITH storage providers
- Software/tools ecosystem highlighted
- Conference-ready professional presentation
- Multi-stakeholder appeal (creators + institutions)

---

**Next Action**: Complete deep research of canonical library for software/tools, about/mission, technology positioning, and resources inventory.